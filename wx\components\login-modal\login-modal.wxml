<!--components/login-modal/login-modal.wxml-->
<t-overlay visible="{{show}}" z-index="2000" bind:click="preventTouchMove">
  <view class="login-modal-container">
    <view class="login-modal-content" catchtap="preventTouchMove">

      <!-- 登录表单 -->
      <view wx:if="{{!showUserInfoForm}}" class="login-form">
        <!-- 登录头部 -->
        <view class="login-header">
          <view class="app-logo">
            <t-icon name="logo-wechat" size="120rpx" color="var(--td-brand-color)" class="logo-icon" />
            <view class="logo-glow"></view>
          </view>
          <text class="app-name">激活码生成器</text>
          <text class="welcome-text">欢迎使用微信小程序</text>
          <view class="header-decoration"></view>
        </view>

        <!-- 登录说明 -->
        <view class="login-description">
          <text class="desc-title">为什么需要登录？</text>
          <view class="benefits-grid">
            <view class="benefit-item">
              <view class="benefit-icon">
                <t-icon name="lock-on" size="40rpx" color="var(--td-brand-color)" />
              </view>
              <view class="benefit-content">
                <text class="benefit-title">数据安全</text>
                <text class="benefit-desc">保护您的激活码数据</text>
              </view>
            </view>
            <view class="benefit-item">
              <view class="benefit-icon">
                <t-icon name="cloud" size="40rpx" color="var(--td-success-color)" />
              </view>
              <view class="benefit-content">
                <text class="benefit-title">云端同步</text>
                <text class="benefit-desc">多设备无缝访问</text>
              </view>
            </view>
            <view class="benefit-item">
              <view class="benefit-icon">
                <t-icon name="chart-bar" size="40rpx" color="var(--td-warning-color)" />
              </view>
              <view class="benefit-content">
                <text class="benefit-title">个性统计</text>
                <text class="benefit-desc">专属成就系统</text>
              </view>
            </view>
            <view class="benefit-item">
              <view class="benefit-icon">
                <t-icon name="gift" size="40rpx" color="var(--td-error-color)" />
              </view>
              <view class="benefit-content">
                <text class="benefit-title">专属福利</text>
                <text class="benefit-desc">活动通知推送</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 登录按钮 -->
        <view class="login-actions">
          <view class="login-btn-wrapper">
            <t-button
              theme="primary"
              size="large"
              block
              loading="{{loginLoading}}"
              disabled="{{loginLoading}}"
              bindtap="handleLogin"
              icon="logo-wechat"
              class="login-btn"
            >
              <text wx:if="{{!loginLoading}}">微信授权登录</text>
              <text wx:else>登录中...</text>
            </t-button>
            <view class="btn-glow"></view>
          </view>

          <view class="privacy-notice">
            <text class="privacy-text">登录即表示同意</text>
            <text class="privacy-link" bindtap="showPrivacyPolicy">《隐私政策》</text>
            <text class="privacy-text">和</text>
            <text class="privacy-link" bindtap="showUserAgreement">《用户协议》</text>
          </view>
        </view>

        <!-- 跳过登录（可选） -->
        <view class="skip-section" wx:if="{{allowSkip}}">
          <t-button
            theme="light"
            variant="text"
            size="medium"
            bindtap="handleSkip"
          >
            暂不登录，体验基础功能
          </t-button>
        </view>
      </view>

      <!-- 用户信息填写表单 -->
      <view wx:if="{{showUserInfoForm}}" class="user-info-form">
        <!-- 表单头部 -->
        <view class="form-header">
          <t-icon name="user" size="80rpx" color="var(--td-brand-color)" class="mb-16" />
          <text class="form-title">完善个人信息</text>
          <text class="form-desc">请选择头像并输入昵称</text>
        </view>

        <!-- 头像选择 -->
        <view class="avatar-section">
          <text class="section-label">选择头像</text>
          <button
            class="avatar-button"
            open-type="chooseAvatar"
            bind:chooseavatar="onChooseAvatar"
          >
            <image
              class="avatar-image"
              src="{{tempAvatarUrl || '/images/default-avatar.svg'}}"
              mode="aspectFill"
            />
            <view class="avatar-overlay">
              <t-icon name="camera" size="48rpx" color="var(--td-text-color-anti)" />
            </view>
          </button>
        </view>

        <!-- 昵称输入 -->
        <view class="nickname-section">
          <text class="section-label">输入昵称</text>
          <input
            class="nickname-input"
            type="nickname"
            placeholder="请输入您的昵称"
            value="{{tempNickName}}"
            bindinput="onNickNameInput"
            maxlength="20"
          />
        </view>

        <!-- 操作按钮 -->
        <view class="form-actions">
          <t-button
            theme="primary"
            size="large"
            block
            bindtap="completeUserInfo"
            class="mb-16"
          >
            完成
          </t-button>
          <t-button
            theme="light"
            variant="outline"
            size="medium"
            bindtap="cancelUserInfo"
          >
            取消
          </t-button>
        </view>
      </view>

    </view>
  </view>
</t-overlay>

<!-- 隐私政策弹窗 -->
<t-dialog
  visible="{{showPolicy}}"
  title="{{policyTitle}}"
  content="{{policyContent}}"
  confirm-btn="我知道了"
  bind:confirm="hidePolicy"
  bind:close="hidePolicy"
/>
