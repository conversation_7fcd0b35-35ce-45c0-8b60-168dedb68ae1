/**app.wxss**/
@import 'miniprogram_npm/tdesign-miniprogram/miniprogram_dist/common/style/index.wxss';

/* 全局变量 */
page {
  /* TDesign 设计令牌 */
  --td-brand-color: #0052d9;
  --td-brand-color-light: #e7f3ff;
  --td-brand-color-focus: #0034b5;
  --td-brand-color-disabled: #b5c7ff;
  --td-success-color: #00a870;
  --td-warning-color: #ed7b2f;
  --td-error-color: #d54941;

  /* 文本颜色 */
  --td-text-color-primary: #000000;
  --td-text-color-secondary: #666666;
  --td-text-color-placeholder: #bbbbbb;
  --td-text-color-disabled: #cccccc;
  --td-text-color-anti: #ffffff;

  /* 背景颜色 */
  --td-bg-color-page: #f5f5f5;
  --td-bg-color-container: #ffffff;
  --td-bg-color-card: #ffffff;
  --td-bg-color-component: #f3f3f3;
  --td-bg-color-component-disabled: #f8f8f8;

  /* 灰度色阶 */
  --td-gray-color-1: #f3f3f3;
  --td-gray-color-2: #eeeeee;
  --td-gray-color-3: #e7e7e7;
  --td-gray-color-4: #dcdcdc;
  --td-gray-color-5: #c5c5c5;
  --td-gray-color-6: #a6a6a6;
  --td-gray-color-7: #8b8b8b;
  --td-gray-color-8: #777777;
  --td-gray-color-9: #5e5e5e;
  --td-gray-color-10: #4b4b4b;
  --td-gray-color-11: #383838;
  --td-gray-color-12: #2c2c2c;
  --td-gray-color-13: #242424;
  --td-gray-color-14: #181818;

  /* 边框颜色 */
  --td-border-level-1-color: #e7e7e7;
  --td-border-level-2-color: #dcdcdc;

  /* 阴影 */
  --td-shadow-1: 0 1rpx 10rpx rgba(0, 0, 0, 0.05), 0 4rpx 20rpx rgba(0, 0, 0, 0.08), 0 10rpx 40rpx rgba(0, 0, 0, 0.1);
  --td-shadow-2: 0 2rpx 20rpx rgba(0, 0, 0, 0.08), 0 8rpx 40rpx rgba(0, 0, 0, 0.12), 0 20rpx 80rpx rgba(0, 0, 0, 0.16);
  --td-shadow-3: 0 4rpx 40rpx rgba(0, 0, 0, 0.12), 0 16rpx 80rpx rgba(0, 0, 0, 0.16), 0 40rpx 160rpx rgba(0, 0, 0, 0.2);

  /* 圆角 */
  --td-radius-small: 6rpx;
  --td-radius-default: 12rpx;
  --td-radius-medium: 16rpx;
  --td-radius-large: 24rpx;
  --td-radius-extra-large: 32rpx;
  --td-radius-round: 50%;

  /* 间距系统 */
  --td-spacer-1: 8rpx;
  --td-spacer-2: 16rpx;
  --td-spacer-3: 24rpx;
  --td-spacer-4: 32rpx;
  --td-spacer-5: 40rpx;
  --td-spacer-6: 48rpx;
  --td-spacer-7: 56rpx;
  --td-spacer-8: 64rpx;

  /* 字体大小系统 */
  --td-font-size-xs: 20rpx;
  --td-font-size-s: 24rpx;
  --td-font-size-base: 28rpx;
  --td-font-size-m: 32rpx;
  --td-font-size-l: 36rpx;
  --td-font-size-xl: 40rpx;
  --td-font-size-xxl: 48rpx;

  /* 字体权重 */
  --td-font-weight-normal: 400;
  --td-font-weight-medium: 500;
  --td-font-weight-semibold: 600;
  --td-font-weight-bold: 700;

  /* 行高系统 */
  --td-line-height-tight: 1.2;
  --td-line-height-base: 1.4;
  --td-line-height-relaxed: 1.6;
  --td-line-height-loose: 1.8;

  /* 过渡动画 */
  --td-transition-fast: 0.1s ease;
  --td-transition-base: 0.2s ease;
  --td-transition-slow: 0.3s ease;

  background-color: var(--td-bg-color-page);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  color: var(--td-text-color-primary);
}

/* 安全区域适配 */
.safe-area-inset-top {
  padding-top: env(safe-area-inset-top);
}

.safe-area-inset-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}

.safe-area-inset-left {
  padding-left: env(safe-area-inset-left);
}

.safe-area-inset-right {
  padding-right: env(safe-area-inset-right);
}

/* 通用容器样式 */
.container {
  min-height: 100vh;
  background-color: var(--td-bg-color-page);
}

.page-container {
  padding: var(--td-spacer-4);
  padding-top: var(--td-spacer-2);
}

/* 通用卡片样式 */
.card {
  background-color: var(--td-bg-color-card);
  border-radius: var(--td-radius-medium);
  padding: var(--td-spacer-4);
  box-shadow: var(--td-shadow-1);
  margin-bottom: var(--td-spacer-3);
  border: 1rpx solid var(--td-border-level-1-color);
  transition: box-shadow 0.2s ease, transform 0.2s ease;
}

.card:hover {
  box-shadow: var(--td-shadow-2);
  transform: translateY(-2rpx);
}

.card-large {
  padding: var(--td-spacer-6);
  border-radius: var(--td-radius-large);
  box-shadow: var(--td-shadow-2);
}

.card-small {
  padding: var(--td-spacer-3);
  border-radius: var(--td-radius-small);
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: var(--td-spacer-3);
}

.card-header-center {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--td-spacer-3);
}

.card-title {
  font-size: 36rpx;
  font-weight: 600;
  color: var(--td-text-color-primary);
  line-height: 1.4;
}

.card-subtitle {
  font-size: 28rpx;
  color: var(--td-text-color-secondary);
  margin-top: var(--td-spacer-1);
  line-height: 1.5;
}

.card-content {
  color: var(--td-text-color-primary);
  line-height: 1.6;
}

.card-footer {
  margin-top: var(--td-spacer-4);
  padding-top: var(--td-spacer-3);
  border-top: 1rpx solid var(--td-border-level-1-color);
}

/* 响应式断点 */
@media screen and (max-width: 375px) {
  .page-container {
    padding: var(--td-spacer-3);
  }

  .card {
    padding: var(--td-spacer-3);
  }

  .card-large {
    padding: var(--td-spacer-4);
  }

  .card-small {
    padding: var(--td-spacer-2);
  }
}

@media screen and (min-width: 768px) {
  .page-container {
    padding: var(--td-spacer-6);
    max-width: 750rpx;
    margin: 0 auto;
  }

  .card {
    padding: var(--td-spacer-5);
  }
}

@media screen and (orientation: landscape) and (max-height: 500px) {
  .page-container {
    padding: var(--td-spacer-2) var(--td-spacer-4);
  }

  .card {
    padding: var(--td-spacer-3);
    margin-bottom: var(--td-spacer-2);
  }
}

/* 文本样式 */
.text-primary {
  color: var(--td-text-color-primary);
}

.text-secondary {
  color: var(--td-text-color-secondary);
}

.text-placeholder {
  color: var(--td-text-color-placeholder);
}

.text-brand {
  color: var(--td-brand-color);
}

.text-success {
  color: var(--td-success-color);
}

.text-warning {
  color: var(--td-warning-color);
}

.text-error {
  color: var(--td-error-color);
}

/* 通用工具类 */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

/* 间距工具类 - 使用设计令牌 */
.mt-1 { margin-top: var(--td-spacer-1); }
.mt-2 { margin-top: var(--td-spacer-2); }
.mt-3 { margin-top: var(--td-spacer-3); }
.mt-4 { margin-top: var(--td-spacer-4); }
.mt-5 { margin-top: var(--td-spacer-5); }
.mt-6 { margin-top: var(--td-spacer-6); }

.mb-1 { margin-bottom: var(--td-spacer-1); }
.mb-2 { margin-bottom: var(--td-spacer-2); }
.mb-3 { margin-bottom: var(--td-spacer-3); }
.mb-4 { margin-bottom: var(--td-spacer-4); }
.mb-5 { margin-bottom: var(--td-spacer-5); }
.mb-6 { margin-bottom: var(--td-spacer-6); }

.ml-1 { margin-left: var(--td-spacer-1); }
.ml-2 { margin-left: var(--td-spacer-2); }
.ml-3 { margin-left: var(--td-spacer-3); }
.ml-4 { margin-left: var(--td-spacer-4); }

.mr-1 { margin-right: var(--td-spacer-1); }
.mr-2 { margin-right: var(--td-spacer-2); }
.mr-3 { margin-right: var(--td-spacer-3); }
.mr-4 { margin-right: var(--td-spacer-4); }

.p-1 { padding: var(--td-spacer-1); }
.p-2 { padding: var(--td-spacer-2); }
.p-3 { padding: var(--td-spacer-3); }
.p-4 { padding: var(--td-spacer-4); }
.p-5 { padding: var(--td-spacer-5); }
.p-6 { padding: var(--td-spacer-6); }

.px-1 { padding-left: var(--td-spacer-1); padding-right: var(--td-spacer-1); }
.px-2 { padding-left: var(--td-spacer-2); padding-right: var(--td-spacer-2); }
.px-3 { padding-left: var(--td-spacer-3); padding-right: var(--td-spacer-3); }
.px-4 { padding-left: var(--td-spacer-4); padding-right: var(--td-spacer-4); }

.py-1 { padding-top: var(--td-spacer-1); padding-bottom: var(--td-spacer-1); }
.py-2 { padding-top: var(--td-spacer-2); padding-bottom: var(--td-spacer-2); }
.py-3 { padding-top: var(--td-spacer-3); padding-bottom: var(--td-spacer-3); }
.py-4 { padding-top: var(--td-spacer-4); padding-bottom: var(--td-spacer-4); }

/* 兼容旧的间距类 */
.mt-10 { margin-top: 20rpx; }
.mt-20 { margin-top: 40rpx; }
.mt-30 { margin-top: 60rpx; }
.mt-40 { margin-top: 80rpx; }

.mb-8 { margin-bottom: var(--td-spacer-2); }
.mb-10 { margin-bottom: 20rpx; }
.mb-12 { margin-bottom: var(--td-spacer-3); }
.mb-16 { margin-bottom: var(--td-spacer-4); }
.mb-20 { margin-bottom: var(--td-spacer-5); }
.mb-24 { margin-bottom: var(--td-spacer-6); }
.mb-30 { margin-bottom: 60rpx; }
.mb-40 { margin-bottom: 80rpx; }

.mr-8 { margin-right: var(--td-spacer-2); }
.mr-12 { margin-right: var(--td-spacer-3); }
.mr-16 { margin-right: var(--td-spacer-4); }

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-1 {
  flex: 1;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

/* 特殊卡片样式 */
.card-elevated {
  background: var(--td-bg-color-card);
  border-radius: var(--td-radius-large);
  padding: var(--td-spacer-5);
  margin-bottom: var(--td-spacer-4);
  box-shadow: var(--td-shadow-2);
  border: 1rpx solid var(--td-border-level-1-color);
}

.card-gradient {
  background: linear-gradient(135deg, var(--td-brand-color-light) 0%, var(--td-bg-color-card) 100%);
  border: 1rpx solid var(--td-brand-color);
}

/* 激活码样式 */
.activation-code {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Consolas', 'Courier New', monospace;
  font-weight: 600;
  color: var(--td-brand-color);
  word-break: break-all;
  letter-spacing: 1rpx;
  font-size: 32rpx;
  line-height: 1.4;
}

/* 状态样式 */
.status-success {
  color: var(--td-success-color);
}

.status-warning {
  color: var(--td-warning-color);
}

.status-danger {
  color: var(--td-error-color);
}

.status-info {
  color: var(--td-brand-color);
}

/* 背景状态样式 */
.bg-success {
  background-color: var(--td-success-color);
  color: var(--td-text-color-anti);
}

.bg-warning {
  background-color: var(--td-warning-color);
  color: var(--td-text-color-anti);
}

.bg-danger {
  background-color: var(--td-error-color);
  color: var(--td-text-color-anti);
}

.bg-info {
  background-color: var(--td-brand-color);
  color: var(--td-text-color-anti);
}

/* 浅色背景状态样式 */
.bg-success-light {
  background-color: var(--td-brand-color-light);
  color: var(--td-success-color);
}

.bg-warning-light {
  background-color: #fff7e6;
  color: var(--td-warning-color);
}

.bg-danger-light {
  background-color: #fff2f0;
  color: var(--td-error-color);
}

.bg-info-light {
  background-color: var(--td-brand-color-light);
  color: var(--td-brand-color);
}

/* 字体大小工具类 */
.text-xs { font-size: var(--td-font-size-xs); }
.text-s { font-size: var(--td-font-size-s); }
.text-base { font-size: var(--td-font-size-base); }
.text-m { font-size: var(--td-font-size-m); }
.text-l { font-size: var(--td-font-size-l); }
.text-xl { font-size: var(--td-font-size-xl); }
.text-xxl { font-size: var(--td-font-size-xxl); }

/* 字体权重工具类 */
.font-normal { font-weight: var(--td-font-weight-normal); }
.font-medium { font-weight: var(--td-font-weight-medium); }
.font-semibold { font-weight: var(--td-font-weight-semibold); }
.font-bold { font-weight: var(--td-font-weight-bold); }

/* 行高工具类 */
.leading-tight { line-height: var(--td-line-height-tight); }
.leading-base { line-height: var(--td-line-height-base); }
.leading-relaxed { line-height: var(--td-line-height-relaxed); }
.leading-loose { line-height: var(--td-line-height-loose); }

/* 圆角工具类 */
.rounded-none { border-radius: 0; }
.rounded-small { border-radius: var(--td-radius-small); }
.rounded { border-radius: var(--td-radius-default); }
.rounded-medium { border-radius: var(--td-radius-medium); }
.rounded-large { border-radius: var(--td-radius-large); }
.rounded-xl { border-radius: var(--td-radius-extra-large); }
.rounded-full { border-radius: var(--td-radius-round); }

/* 阴影工具类 */
.shadow-none { box-shadow: none; }
.shadow-1 { box-shadow: var(--td-shadow-1); }
.shadow-2 { box-shadow: var(--td-shadow-2); }
.shadow-3 { box-shadow: var(--td-shadow-3); }

/* 过渡动画工具类 */
.transition-fast { transition: all var(--td-transition-fast); }
.transition { transition: all var(--td-transition-base); }
.transition-slow { transition: all var(--td-transition-slow); }

/* 边框工具类 */
.border { border: 1rpx solid var(--td-border-level-1-color); }
.border-2 { border: 2rpx solid var(--td-border-level-2-color); }
.border-brand { border: 1rpx solid var(--td-brand-color); }
.border-success { border: 1rpx solid var(--td-success-color); }
.border-warning { border: 1rpx solid var(--td-warning-color); }
.border-error { border: 1rpx solid var(--td-error-color); }

.border-t { border-top: 1rpx solid var(--td-border-level-1-color); }
.border-b { border-bottom: 1rpx solid var(--td-border-level-1-color); }
.border-l { border-left: 1rpx solid var(--td-border-level-1-color); }
.border-r { border-right: 1rpx solid var(--td-border-level-1-color); }

/* 透明度工具类 */
.opacity-0 { opacity: 0; }
.opacity-25 { opacity: 0.25; }
.opacity-50 { opacity: 0.5; }
.opacity-75 { opacity: 0.75; }
.opacity-100 { opacity: 1; }

/* 溢出处理 */
.overflow-hidden { overflow: hidden; }
.overflow-scroll { overflow: scroll; }
.overflow-auto { overflow: auto; }

/* 文本溢出 */
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-ellipsis-2 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}

.text-ellipsis-3 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 定位工具类 */
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }
.sticky { position: sticky; }

/* 宽高工具类 */
.w-full { width: 100%; }
.h-full { height: 100%; }
.min-h-screen { min-height: 100vh; }

/* 显示隐藏 */
.hidden { display: none; }
.block { display: block; }
.inline { display: inline; }
.inline-block { display: inline-block; }

/* 交互状态 */
.cursor-pointer { cursor: pointer; }
.pointer-events-none { pointer-events: none; }

/* 用户选择 */
.select-none { user-select: none; }
.select-text { user-select: text; }
.select-all { user-select: all; }
