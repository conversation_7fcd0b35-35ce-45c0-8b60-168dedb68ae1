/* pages/stats/stats.wxss */
.container {
  min-height: 100vh;
  background-color: var(--td-bg-color-page);
}

.page-content {
  padding: var(--td-spacer-4);
  padding-top: var(--td-spacer-2);
}

/* 卡片样式 */
.stats-card,
.today-card,
.usage-card,
.achievement-card {
  position: relative;
  overflow: hidden;
  border: 1rpx solid var(--td-border-level-1-color);
}

.stats-bg,
.today-bg,
.achievement-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 20% 30%, rgba(0, 82, 217, 0.05) 0%, transparent 50%),
              radial-gradient(circle at 80% 70%, rgba(0, 167, 112, 0.05) 0%, transparent 50%);
}

/* 卡片标题 */
.card-header {
  display: flex;
  align-items: center;
  position: relative;
  z-index: 2;
}

.header-icon {
  position: relative;
  margin-right: var(--td-spacer-3);
}

.icon-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60rpx;
  height: 60rpx;
  background: radial-gradient(circle, rgba(0, 82, 217, 0.2) 0%, transparent 70%);
  border-radius: 50%;
  animation: pulse 2s ease-in-out infinite;
}

.icon-pulse {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60rpx;
  height: 60rpx;
  background: radial-gradient(circle, rgba(0, 167, 112, 0.2) 0%, transparent 70%);
  border-radius: 50%;
  animation: pulse 2s ease-in-out infinite;
}

.icon-sparkle {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60rpx;
  height: 60rpx;
  background: radial-gradient(circle, rgba(237, 123, 47, 0.2) 0%, transparent 70%);
  border-radius: 50%;
  animation: sparkle 3s ease-in-out infinite;
}

@keyframes sparkle {
  0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 0.7; }
  50% { transform: translate(-50%, -50%) scale(1.2); opacity: 0.3; }
}

.card-title {
  font-size: var(--td-font-size-l);
  font-weight: var(--td-font-weight-bold);
  color: var(--td-text-color-primary);
  flex: 1;
}

/* 统计网格 */
.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--td-spacer-3);
  position: relative;
  z-index: 2;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: var(--td-spacer-2);
  padding: var(--td-spacer-4);
  background: var(--td-bg-color-card);
  border-radius: var(--td-radius-medium);
  border: 1rpx solid var(--td-border-level-1-color);
  position: relative;
  overflow: hidden;
  transition: all var(--td-transition-base);
}

.stat-card:hover {
  transform: translateY(-2rpx);
  box-shadow: var(--td-shadow-1);
}

.stat-card.primary {
  border-color: var(--td-brand-color);
}

.stat-card.success {
  border-color: var(--td-success-color);
}

.stat-card.warning {
  border-color: var(--td-warning-color);
}

.stat-card.info {
  border-color: var(--td-error-color);
}

.stat-icon {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--td-gray-color-1);
  border-radius: var(--td-radius-small);
  position: relative;
  z-index: 2;
}

.stat-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.stat-number {
  font-size: var(--td-font-size-xl);
  font-weight: var(--td-font-weight-bold);
  color: var(--td-text-color-primary);
  line-height: 1;
  margin-bottom: 4rpx;
}

.stat-label {
  font-size: var(--td-font-size-s);
  color: var(--td-text-color-secondary);
  line-height: 1;
}

.stat-decoration {
  position: absolute;
  top: 0;
  right: 0;
  width: 40rpx;
  height: 40rpx;
  background: radial-gradient(circle, rgba(0, 82, 217, 0.1) 0%, transparent 70%);
  border-radius: 0 var(--td-radius-medium) 0 var(--td-radius-medium);
}

/* 今日统计 */
.today-badge {
  background: var(--td-success-color);
  color: var(--td-text-color-anti);
  padding: 4rpx 12rpx;
  border-radius: var(--td-radius-small);
  font-size: var(--td-font-size-xs);
  font-weight: var(--td-font-weight-semibold);
  animation: blink 2s ease-in-out infinite;
}

.today-stats {
  display: flex;
  flex-direction: column;
  gap: var(--td-spacer-3);
  position: relative;
  z-index: 2;
}

.today-item {
  display: flex;
  align-items: center;
  gap: var(--td-spacer-3);
  padding: var(--td-spacer-3);
  background: var(--td-bg-color-card);
  border-radius: var(--td-radius-medium);
  border: 1rpx solid var(--td-border-level-1-color);
  transition: all var(--td-transition-base);
}

.today-item:hover {
  transform: translateX(4rpx);
  box-shadow: var(--td-shadow-1);
}

.today-icon {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--td-gray-color-1);
  border-radius: var(--td-radius-small);
}

.today-content {
  flex: 1;
  display: flex;
  align-items: center;
  gap: var(--td-spacer-2);
}

.today-number {
  font-size: var(--td-font-size-l);
  font-weight: var(--td-font-weight-bold);
  color: var(--td-text-color-primary);
  min-width: 60rpx;
}

.today-label {
  font-size: var(--td-font-size-base);
  color: var(--td-text-color-secondary);
}

.today-trend {
  width: 24rpx;
  height: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: var(--td-gray-color-2);
  opacity: 0.5;
  transition: all var(--td-transition-base);
}

.today-trend.active {
  background: rgba(0, 167, 112, 0.1);
  opacity: 1;
}

/* 成就系统 */
.achievement-count {
  background: var(--td-warning-color);
  color: var(--td-text-color-anti);
  padding: 4rpx 12rpx;
  border-radius: var(--td-radius-small);
  font-size: var(--td-font-size-xs);
  font-weight: var(--td-font-weight-semibold);
}

.achievements {
  display: flex;
  flex-direction: column;
  gap: var(--td-spacer-3);
  position: relative;
  z-index: 2;
}

.achievement-item {
  transition: all var(--td-transition-base);
}

.achievement-item.unlocked {
  transform: scale(1.02);
}

.achievement-card-inner {
  padding: var(--td-spacer-4);
  background: var(--td-bg-color-card);
  border-radius: var(--td-radius-medium);
  border: 1rpx solid var(--td-border-level-1-color);
  position: relative;
  overflow: hidden;
}

.achievement-item.unlocked .achievement-card-inner {
  border-color: var(--td-success-color);
  background: linear-gradient(135deg, rgba(0, 167, 112, 0.05) 0%, var(--td-bg-color-card) 100%);
}

.achievement-header {
  display: flex;
  align-items: center;
  gap: var(--td-spacer-3);
  margin-bottom: var(--td-spacer-3);
}

.achievement-icon-wrapper {
  position: relative;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.achievement-icon {
  font-size: 40rpx;
  position: relative;
  z-index: 2;
}

.icon-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 50%;
  transition: all var(--td-transition-base);
}

.icon-bg.locked {
  background: var(--td-gray-color-2);
}

.icon-bg.unlocked {
  background: linear-gradient(135deg, var(--td-success-color) 0%, #00a870 100%);
  box-shadow: 0 4rpx 12rpx rgba(0, 167, 112, 0.3);
}

.unlock-effect {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80rpx;
  height: 80rpx;
  border: 2rpx solid var(--td-success-color);
  border-radius: 50%;
  opacity: 0;
  animation: unlock-ripple 2s ease-out infinite;
}

@keyframes unlock-ripple {
  0% { transform: translate(-50%, -50%) scale(0.8); opacity: 1; }
  100% { transform: translate(-50%, -50%) scale(1.5); opacity: 0; }
}

.achievement-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--td-spacer-1);
}

.achievement-title {
  font-size: var(--td-font-size-m);
  font-weight: var(--td-font-weight-semibold);
  color: var(--td-text-color-primary);
}

.achievement-desc {
  font-size: var(--td-font-size-base);
  color: var(--td-text-color-secondary);
  line-height: var(--td-line-height-base);
}

.achievement-status {
  align-self: flex-start;
}

.status-badge {
  display: flex;
  align-items: center;
  gap: var(--td-spacer-1);
  padding: 6rpx 12rpx;
  border-radius: var(--td-radius-small);
  transition: all var(--td-transition-base);
}

.status-badge.unlocked {
  background: rgba(0, 167, 112, 0.1);
  border: 1rpx solid var(--td-success-color);
}

.status-badge.locked {
  background: var(--td-gray-color-1);
  border: 1rpx solid var(--td-border-level-1-color);
}

.status-text {
  font-size: var(--td-font-size-xs);
  font-weight: var(--td-font-weight-medium);
  color: var(--td-text-color-secondary);
}

.achievement-progress {
  border-top: 1rpx solid var(--td-border-level-1-color);
  padding-top: var(--td-spacer-3);
}

.progress-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--td-spacer-2);
}

.progress-text {
  font-size: var(--td-font-size-s);
  color: var(--td-text-color-placeholder);
}

.progress-percent {
  font-size: var(--td-font-size-s);
  font-weight: var(--td-font-weight-semibold);
  color: var(--td-brand-color);
}

.progress-bar {
  height: 12rpx;
  background: var(--td-gray-color-2);
  border-radius: 6rpx;
  overflow: hidden;
  position: relative;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--td-brand-color) 0%, var(--td-success-color) 100%);
  border-radius: 6rpx;
  transition: width 0.5s ease;
  position: relative;
  overflow: hidden;
}

.progress-shine {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: shine 2s ease-in-out infinite;
}

/* 响应式适配 */
@media screen and (max-width: 375px) {
  .page-content {
    padding: var(--td-spacer-3);
    padding-top: var(--td-spacer-2);
  }

  .stats-card,
  .today-card,
  .usage-card,
  .achievement-card {
    padding: var(--td-spacer-3);
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: var(--td-spacer-2);
  }

  .stat-card {
    padding: var(--td-spacer-3);
  }

  .today-stats {
    gap: var(--td-spacer-2);
  }

  .today-item {
    padding: var(--td-spacer-2);
  }

  .today-content {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--td-spacer-1);
  }

  .achievement-card-inner {
    padding: var(--td-spacer-3);
  }

  .achievement-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--td-spacer-2);
  }

  .achievement-icon-wrapper {
    align-self: center;
  }

  .achievement-status {
    align-self: center;
  }

  .card-title {
    font-size: var(--td-font-size-m);
  }

  .stat-number {
    font-size: var(--td-font-size-l);
  }

  .today-number {
    font-size: var(--td-font-size-m);
  }
}

@media screen and (min-width: 768px) {
  .page-content {
    max-width: 750rpx;
    margin: 0 auto;
    padding: var(--td-spacer-6);
  }

  .stats-card,
  .today-card,
  .usage-card,
  .achievement-card {
    padding: var(--td-spacer-6);
  }

  .stats-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: var(--td-spacer-4);
  }

  .stat-card {
    padding: var(--td-spacer-4);
  }

  .today-item {
    padding: var(--td-spacer-4);
  }

  .achievement-card-inner {
    padding: var(--td-spacer-5);
  }
}

@media screen and (min-width: 1024px) {
  .page-content {
    max-width: 900rpx;
    padding: var(--td-spacer-8);
  }

  .stats-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: var(--td-spacer-5);
  }

  .achievements {
    gap: var(--td-spacer-4);
  }
}

/* 横屏适配 */
@media screen and (orientation: landscape) and (max-height: 500px) {
  .page-content {
    padding: var(--td-spacer-2) var(--td-spacer-4);
  }

  .stats-card,
  .today-card,
  .usage-card,
  .achievement-card {
    padding: var(--td-spacer-3);
  }

  .stats-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: var(--td-spacer-2);
  }

  .today-stats {
    flex-direction: row;
    gap: var(--td-spacer-2);
  }

  .today-item {
    flex: 1;
    padding: var(--td-spacer-2);
  }

  .today-content {
    flex-direction: column;
    align-items: center;
  }

  .achievement-header {
    flex-direction: row;
    align-items: center;
  }

  .achievements {
    gap: var(--td-spacer-2);
  }

  .achievement-card-inner {
    padding: var(--td-spacer-3);
  }
}

/* 超小屏幕适配 */
@media screen and (max-width: 320px) {
  .page-content {
    padding: var(--td-spacer-2);
  }

  .stats-card,
  .today-card,
  .usage-card,
  .achievement-card {
    padding: var(--td-spacer-2);
  }

  .card-title {
    font-size: var(--td-font-size-base);
  }

  .stat-number {
    font-size: var(--td-font-size-m);
  }

  .today-number {
    font-size: var(--td-font-size-base);
  }

  .achievement-title {
    font-size: var(--td-font-size-base);
  }

  .achievement-desc {
    font-size: var(--td-font-size-s);
  }
}

/* 操作按钮 */
.actions {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  flex: 1;
}
