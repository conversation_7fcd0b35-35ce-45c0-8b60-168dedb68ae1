# 🎨 TDesign 组件使用规范指南

## 📋 概述

本文档规定了微信小程序中 TDesign 组件的标准使用方式，确保整个应用的视觉一致性和用户体验统一性。

## 🎯 核心原则

### 1. 一致性 (Consistency)
- 相同功能的组件使用相同的属性配置
- 统一的颜色、尺寸、间距规范
- 标准化的交互模式

### 2. 可维护性 (Maintainability)
- 使用设计令牌而非硬编码值
- 遵循命名规范
- 保持代码结构清晰

### 3. 可访问性 (Accessibility)
- 合理的颜色对比度
- 适当的触摸区域大小
- 语义化的组件使用

## 🧩 组件使用规范

### 按钮组件 (t-button)

#### 标准配置
```xml
<!-- 主要按钮 -->
<t-button theme="primary" size="large" block>
  主要操作
</t-button>

<!-- 次要按钮 -->
<t-button theme="light" variant="outline" size="medium">
  次要操作
</t-button>

<!-- 危险操作按钮 -->
<t-button theme="danger" variant="outline" size="medium">
  危险操作
</t-button>
```

#### 尺寸规范
- `large`: 主要操作按钮，高度 88rpx
- `medium`: 次要操作按钮，高度 64rpx
- `small`: 辅助操作按钮，高度 48rpx

#### 主题规范
- `primary`: 主要操作（如提交、确认）
- `light`: 次要操作（如取消、返回）
- `danger`: 危险操作（如删除、清空）

### 图标组件 (t-icon)

#### 标准配置
```xml
<!-- 使用设计令牌颜色 -->
<t-icon name="help-circle" size="48rpx" color="var(--td-brand-color)" />

<!-- 导航栏图标 -->
<t-icon name="setting" size="48rpx" color="var(--td-text-color-anti)" />

<!-- 功能图标 -->
<t-icon name="file-copy" size="32rpx" color="var(--td-text-color-secondary)" />
```

#### 尺寸规范
- `64rpx`: 大型装饰图标
- `48rpx`: 导航栏图标、卡片标题图标
- `32rpx`: 列表项图标、按钮图标
- `24rpx`: 小型辅助图标

#### 颜色规范
- 主要图标: `var(--td-brand-color)`
- 次要图标: `var(--td-text-color-secondary)`
- 反色图标: `var(--td-text-color-anti)`
- 功能色图标: `var(--td-success-color)` / `var(--td-warning-color)` / `var(--td-error-color)`

### 标签组件 (t-tag)

#### 标准配置
```xml
<!-- 状态标签 -->
<t-tag theme="success" variant="light" icon="check-circle">
  已完成
</t-tag>

<!-- 分类标签 -->
<t-tag theme="primary" variant="light">
  专属
</t-tag>

<!-- 警告标签 -->
<t-tag theme="warning" variant="light" icon="time">
  处理中
</t-tag>
```

#### 主题规范
- `success`: 成功状态（绿色）
- `warning`: 警告状态（橙色）
- `danger`: 错误状态（红色）
- `primary`: 主要信息（蓝色）
- `default`: 默认信息（灰色）

### 单元格组件 (t-cell)

#### 标准配置
```xml
<t-cell-group>
  <t-cell 
    title="标题文本" 
    description="描述文本"
    left-icon="icon-name"
    arrow
    bindtap="handleTap"
  />
</t-cell-group>
```

#### 使用规范
- 始终使用 `t-cell-group` 包裹 `t-cell`
- 图标使用语义化名称
- 描述文本保持简洁

### 网格组件 (t-grid)

#### 标准配置
```xml
<t-grid column="2" class="stats-grid">
  <t-grid-item>
    <view class="grid-content">
      <!-- 内容 -->
    </view>
  </t-grid-item>
</t-grid>
```

#### 列数规范
- 2列: 统计数据、对比信息
- 3列: 功能入口、选项卡
- 4列: 小图标网格

## 🎨 样式规范

### 间距系统

#### CSS 类名规范
```css
/* 推荐使用 */
.mb-4    /* margin-bottom: var(--td-spacer-4) */
.mr-3    /* margin-right: var(--td-spacer-3) */
.p-4     /* padding: var(--td-spacer-4) */

/* 避免使用 */
.mb-24   /* 硬编码数值 */
.mr-12   /* 硬编码数值 */
```

#### 间距令牌
- `--td-spacer-1`: 8rpx (最小间距)
- `--td-spacer-2`: 16rpx (小间距)
- `--td-spacer-3`: 24rpx (中等间距)
- `--td-spacer-4`: 32rpx (标准间距)
- `--td-spacer-5`: 40rpx (大间距)
- `--td-spacer-6`: 48rpx (超大间距)

### 颜色系统

#### 文本颜色
```css
.text-primary     /* var(--td-text-color-primary) */
.text-secondary   /* var(--td-text-color-secondary) */
.text-placeholder /* var(--td-text-color-placeholder) */
.text-brand       /* var(--td-brand-color) */
```

#### 背景颜色
```css
.bg-success       /* var(--td-success-color) */
.bg-warning       /* var(--td-warning-color) */
.bg-danger        /* var(--td-error-color) */
.bg-info          /* var(--td-brand-color) */
```

### 圆角系统

#### 圆角令牌
- `--td-radius-small`: 6rpx (小圆角)
- `--td-radius-default`: 12rpx (默认圆角)
- `--td-radius-medium`: 16rpx (中等圆角)
- `--td-radius-large`: 24rpx (大圆角)
- `--td-radius-extra-large`: 32rpx (超大圆角)

## 📱 响应式规范

### 断点系统
```css
/* 小屏幕 */
@media screen and (max-width: 375px) {
  /* 减小间距和字体 */
}

/* 大屏幕 */
@media screen and (min-width: 768px) {
  /* 增加间距，居中布局 */
}

/* 横屏 */
@media screen and (orientation: landscape) and (max-height: 500px) {
  /* 紧凑布局 */
}
```

### 适配原则
1. **移动优先**: 先设计小屏幕，再适配大屏幕
2. **渐进增强**: 基础功能在所有设备上可用
3. **内容优先**: 确保内容在任何设备上都清晰可读

## ✅ 检查清单

### 组件使用检查
- [ ] 按钮使用了正确的 theme 和 size
- [ ] 图标使用了设计令牌颜色
- [ ] 标签使用了合适的 variant
- [ ] 间距使用了统一的 CSS 类

### 样式检查
- [ ] 没有硬编码的颜色值
- [ ] 使用了设计令牌
- [ ] 间距系统一致
- [ ] 圆角规范统一

### 响应式检查
- [ ] 小屏幕设备显示正常
- [ ] 大屏幕设备布局合理
- [ ] 横屏模式适配良好
- [ ] 触摸区域大小合适

## 🚀 最佳实践

### 1. 组件复用
- 创建通用组件封装常用模式
- 避免重复的样式代码
- 保持组件的单一职责

### 2. 性能优化
- 按需引入组件
- 避免不必要的嵌套
- 合理使用条件渲染

### 3. 可维护性
- 使用语义化的类名
- 保持代码结构清晰
- 及时更新文档

## 📚 参考资源

- [TDesign 官方文档](https://tdesign.tencent.com/miniprogram/overview)
- [微信小程序设计指南](https://developers.weixin.qq.com/miniprogram/design/)
- [TDesign 设计令牌](https://tdesign.tencent.com/design/values)
