/* pages/history/history.wxss */
.container {
  min-height: 100vh;
  background-color: var(--td-bg-color-page);
}

.page-content {
  padding: var(--td-spacer-4);
  padding-top: var(--td-spacer-2);
}

/* 统计卡片 */
.stats-card {
  position: relative;
  overflow: hidden;
  border: 1rpx solid var(--td-brand-color-light);
}

.stats-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 20% 30%, rgba(0, 82, 217, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 80% 70%, rgba(0, 167, 112, 0.1) 0%, transparent 50%);
}

.card-header {
  display: flex;
  align-items: center;
  position: relative;
  z-index: 2;
}

.header-icon {
  position: relative;
  margin-right: var(--td-spacer-3);
}

.icon-pulse {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60rpx;
  height: 60rpx;
  background: radial-gradient(circle, rgba(0, 82, 217, 0.2) 0%, transparent 70%);
  border-radius: 50%;
  animation: pulse 2s ease-in-out infinite;
}

.card-title {
  font-size: var(--td-font-size-l);
  font-weight: var(--td-font-weight-bold);
  color: var(--td-text-color-primary);
}

.stats-container {
  display: flex;
  align-items: center;
  position: relative;
  z-index: 2;
}

.stat-card {
  flex: 1;
  display: flex;
  align-items: center;
  gap: var(--td-spacer-2);
  padding: var(--td-spacer-3);
  background: var(--td-bg-color-card);
  border-radius: var(--td-radius-medium);
  border: 1rpx solid var(--td-border-level-1-color);
  transition: all var(--td-transition-base);
}

.stat-card:hover {
  transform: translateY(-2rpx);
  box-shadow: var(--td-shadow-1);
}

.stat-icon {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--td-gray-color-1);
  border-radius: var(--td-radius-small);
}

.stat-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.stat-number {
  font-size: var(--td-font-size-xl);
  font-weight: var(--td-font-weight-bold);
  color: var(--td-brand-color);
  line-height: 1;
  margin-bottom: 4rpx;
}

.stat-label {
  font-size: var(--td-font-size-s);
  color: var(--td-text-color-secondary);
  line-height: 1;
}

.stat-trend {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all var(--td-transition-base);
}

.stat-trend.up {
  background: rgba(0, 167, 112, 0.1);
}

.stat-trend.neutral {
  background: var(--td-gray-color-2);
}

.stat-divider {
  width: 1rpx;
  height: 60rpx;
  background: var(--td-border-level-1-color);
  margin: 0 var(--td-spacer-2);
}

/* 操作栏 */
.action-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--td-spacer-3);
  padding: var(--td-spacer-3);
  background: var(--td-bg-color-card);
  border-radius: var(--td-radius-medium);
  border: 1rpx solid var(--td-border-level-1-color);
  box-shadow: var(--td-shadow-1);
}

.action-group {
  display: flex;
  gap: var(--td-spacer-2);
}

.action-btn {
  transition: all var(--td-transition-base);
}

.export-btn:hover {
  transform: translateY(-1rpx);
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
}

.stats-btn:hover {
  transform: translateY(-1rpx);
  box-shadow: 0 4rpx 8rpx rgba(0, 82, 217, 0.2);
}

.clear-btn:hover {
  transform: translateY(-1rpx);
  box-shadow: 0 4rpx 8rpx rgba(213, 73, 65, 0.2);
}

/* 历史记录列表 */
.history-list {
  background: var(--td-bg-color-card);
  border-radius: var(--td-radius-medium);
  border: 1rpx solid var(--td-border-level-1-color);
  overflow: hidden;
  box-shadow: var(--td-shadow-1);
}

.list-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--td-spacer-4);
  background: var(--td-gray-color-1);
  border-bottom: 1rpx solid var(--td-border-level-1-color);
}

.list-title {
  font-size: var(--td-font-size-m);
  font-weight: var(--td-font-weight-semibold);
  color: var(--td-text-color-primary);
}

.list-count {
  font-size: var(--td-font-size-s);
  color: var(--td-text-color-placeholder);
  background: var(--td-bg-color-card);
  padding: 4rpx 12rpx;
  border-radius: var(--td-radius-small);
  border: 1rpx solid var(--td-border-level-1-color);
}

.history-items {
  display: flex;
  flex-direction: column;
}

.history-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--td-spacer-4);
  border-bottom: 1rpx solid var(--td-border-level-1-color);
  transition: all var(--td-transition-base);
  position: relative;
}

.history-item:last-child {
  border-bottom: none;
}

.history-item:hover {
  background: var(--td-gray-color-1);
}

.history-item:active {
  background: var(--td-gray-color-2);
}

.item-left {
  display: flex;
  align-items: center;
  flex: 1;
  gap: var(--td-spacer-3);
}

.item-icon {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--td-brand-color-light);
  border-radius: var(--td-radius-small);
  border: 1rpx solid var(--td-brand-color);
}

.item-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--td-spacer-1);
}

.item-code {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Consolas', monospace;
  font-size: var(--td-font-size-base);
  font-weight: var(--td-font-weight-semibold);
  color: var(--td-text-color-primary);
  word-break: break-all;
}

.item-meta {
  display: flex;
  align-items: center;
  gap: var(--td-spacer-2);
}

.item-date {
  font-size: var(--td-font-size-s);
  color: var(--td-text-color-placeholder);
}

.item-badge {
  background: var(--td-brand-color);
  color: var(--td-text-color-anti);
  padding: 2rpx 8rpx;
  border-radius: var(--td-radius-small);
  font-size: var(--td-font-size-xs);
  font-weight: var(--td-font-weight-medium);
}

.item-actions {
  display: flex;
  align-items: center;
  gap: var(--td-spacer-2);
}

.copy-action {
  transition: all var(--td-transition-fast);
}

.copy-action:hover {
  transform: scale(1.05);
}

.action-indicator {
  opacity: 0.5;
  transition: opacity var(--td-transition-fast);
}

.history-item:hover .action-indicator {
  opacity: 1;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--td-spacer-8) var(--td-spacer-4);
  text-align: center;
  min-height: 400rpx;
}

.empty-illustration {
  position: relative;
  margin-bottom: var(--td-spacer-6);
}

.empty-icon {
  position: relative;
  z-index: 2;
  opacity: 0.6;
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10rpx); }
}

.empty-decoration {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  gap: 20rpx;
}

.decoration-dot {
  width: 12rpx;
  height: 12rpx;
  background: var(--td-text-color-placeholder);
  border-radius: 50%;
  opacity: 0.3;
  animation: blink 2s ease-in-out infinite;
}

.decoration-dot:nth-child(2) {
  animation-delay: 0.5s;
}

.decoration-dot:nth-child(3) {
  animation-delay: 1s;
}

.empty-content {
  margin-bottom: var(--td-spacer-6);
}

.empty-title {
  display: block;
  font-size: var(--td-font-size-l);
  font-weight: var(--td-font-weight-semibold);
  color: var(--td-text-color-primary);
  margin-bottom: var(--td-spacer-2);
}

.empty-desc {
  display: block;
  font-size: var(--td-font-size-base);
  color: var(--td-text-color-secondary);
  line-height: var(--td-line-height-relaxed);
  white-space: pre-line;
}

.empty-action {
  width: 100%;
  max-width: 400rpx;
}

.generate-btn {
  background: linear-gradient(135deg, var(--td-brand-color) 0%, #0034b5 100%);
  box-shadow: 0 8rpx 24rpx rgba(0, 82, 217, 0.3);
  border: none;
  transition: all var(--td-transition-base);
}

.generate-btn:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 12rpx 32rpx rgba(0, 82, 217, 0.4);
}

/* 响应式适配 */
@media screen and (max-width: 375px) {
  .page-content {
    padding: var(--td-spacer-3);
    padding-top: var(--td-spacer-2);
  }

  .stats-card {
    padding: var(--td-spacer-3);
  }

  .stats-container {
    flex-direction: column;
    gap: var(--td-spacer-2);
  }

  .stat-divider {
    width: 100%;
    height: 1rpx;
    margin: 0;
  }

  .action-bar {
    flex-direction: column;
    gap: var(--td-spacer-2);
  }

  .action-group {
    width: 100%;
    justify-content: space-between;
  }

  .clear-btn {
    width: 100%;
  }

  .history-item {
    padding: var(--td-spacer-3);
  }

  .item-left {
    gap: var(--td-spacer-2);
  }

  .item-code {
    font-size: var(--td-font-size-s);
  }

  .item-actions {
    flex-direction: column;
    gap: var(--td-spacer-1);
  }

  .empty-state {
    padding: var(--td-spacer-6) var(--td-spacer-3);
  }

  .empty-title {
    font-size: var(--td-font-size-m);
  }
}

@media screen and (min-width: 768px) {
  .page-content {
    max-width: 750rpx;
    margin: 0 auto;
    padding: var(--td-spacer-6);
  }

  .stats-card {
    padding: var(--td-spacer-6);
  }

  .history-list {
    padding: var(--td-spacer-4);
  }

  .action-bar {
    padding: var(--td-spacer-4);
  }
}

@media screen and (min-width: 1024px) {
  .page-content {
    max-width: 900rpx;
    padding: var(--td-spacer-8);
  }

  .stats-container {
    gap: var(--td-spacer-4);
  }

  .stat-card {
    padding: var(--td-spacer-5);
  }
}

/* 横屏适配 */
@media screen and (orientation: landscape) and (max-height: 500px) {
  .page-content {
    padding: var(--td-spacer-2) var(--td-spacer-4);
  }

  .stats-card {
    padding: var(--td-spacer-3);
  }

  .action-bar {
    padding: var(--td-spacer-2);
  }

  .history-item {
    padding: var(--td-spacer-2) var(--td-spacer-3);
  }

  .empty-state {
    padding: var(--td-spacer-4) var(--td-spacer-3);
    min-height: 300rpx;
  }
}

/* 超小屏幕适配 */
@media screen and (max-width: 320px) {
  .page-content {
    padding: var(--td-spacer-2);
  }

  .stats-card,
  .action-bar,
  .history-list {
    padding: var(--td-spacer-2);
  }

  .card-title {
    font-size: var(--td-font-size-base);
  }

  .stat-number {
    font-size: var(--td-font-size-l);
  }

  .item-code {
    font-size: 24rpx;
  }
}
