<!--pages/index/index.wxml-->
<view class="container">
  <!-- 导航栏 -->
  <t-navbar
    title="激活码生成器"
    background="var(--td-brand-color)"
    title-color="var(--td-text-color-anti)"
    left-arrow="{{false}}"
  >
    <view slot="right" class="navbar-actions">
      <t-icon name="help-circle" size="48rpx" color="var(--td-text-color-anti)" bindtap="showGuide" class="mr-3" />
      <t-icon name="setting" size="48rpx" color="var(--td-text-color-anti)" bindtap="showDebugInfo" class="mr-3" />
      <t-avatar
        wx:if="{{isLoggedIn}}"
        image="{{userInfo.avatarUrl}}"
        size="small"
        bindtap="logout"
      />
      <t-button
        wx:else
        theme="light"
        size="small"
        bindtap="requireLogin"
      >
        登录
      </t-button>
    </view>
  </t-navbar>

  <!-- 页面内容 -->
  <view class="page-content">
    <!-- 用户信息卡片 -->
    <view wx:if="{{isLoggedIn}}" class="user-card card-gradient mb-4">
      <view class="user-card-bg"></view>
      <view class="user-info-content">
        <view class="user-avatar-wrapper">
          <t-avatar image="{{userInfo.avatarUrl}}" size="large" class="user-avatar" />
          <view class="avatar-ring"></view>
          <view class="online-indicator"></view>
        </view>
        <view class="user-details flex-1">
          <text class="user-name">{{userInfo.nickName}}</text>
          <text class="user-desc">欢迎回来，开始您的激活码之旅！</text>
          <view class="user-stats">
            <view class="stat-item">
              <text class="stat-number">{{stats.totalGenerated}}</text>
              <text class="stat-label">生成数</text>
            </view>
            <view class="stat-divider"></view>
            <view class="stat-item">
              <text class="stat-number">{{stats.totalAdsWatched}}</text>
              <text class="stat-label">观看数</text>
            </view>
          </view>
        </view>
        <view class="user-badge">
          <t-tag theme="success" variant="light" icon="check-circle">已登录</t-tag>
        </view>
      </view>
    </view>

    <!-- 加载状态 -->
    <t-loading
      wx:if="{{loadingState.show}}"
      theme="circular"
      size="large"
      text="{{loadingState.text}}"
      class="loading-container"
    />

    <!-- 主功能卡片 -->
    <view wx:if="{{!showActivationCode && !loadingState.show}}" class="main-card card-elevated mb-4">
      <view class="main-card-bg"></view>
      <view class="card-header-center mb-4">
        <view class="feature-icon">
          <t-icon name="gift" size="64rpx" color="var(--td-brand-color)" />
          <view class="icon-glow"></view>
        </view>
      </view>
      <view class="feature-content">
        <text class="feature-title">获取专属激活码</text>
        <text class="feature-desc">观看激励视频广告，即可免费获取唯一激活码</text>
      </view>

      <view class="action-section">
        <view class="btn-wrapper">
          <t-button
            theme="primary"
            size="large"
            block
            loading="{{adWatching}}"
            disabled="{{!adLoaded || adWatching}}"
            bindtap="watchAd"
            icon="play-circle"
            class="main-action-btn"
          >
            <text wx:if="{{!adWatching}}">观看广告获取激活码</text>
            <text wx:else>广告播放中...</text>
          </t-button>
          <view class="btn-shine"></view>
        </view>

        <view class="ad-status-wrapper">
          <view class="status-indicator {{adLoaded ? 'ready' : 'loading'}}"></view>
          <text class="status-text">
            {{adLoaded ? '广告已准备就绪' : '广告加载中...'}}
          </text>
        </view>
      </view>
    </view>

    <!-- 激活码展示卡片 -->
    <view wx:if="{{showActivationCode}}" class="result-card card-elevated mb-4">
      <view class="success-animation">
        <view class="success-circle">
          <t-icon name="check-circle" size="80rpx" color="var(--td-success-color)" />
        </view>
        <view class="success-ripple"></view>
      </view>

      <view class="result-content">
        <text class="result-title">🎉 激活码生成成功！</text>
        <text class="result-desc">您的专属激活码已准备就绪</text>
      </view>

      <view class="code-display-section">
        <view class="code-container">
          <view class="code-header">
            <text class="code-label">您的激活码</text>
            <view class="code-badge">
              <text class="badge-text">专属</text>
            </view>
          </view>
          <view class="code-content">
            <text class="activation-code">{{currentActivationCode}}</text>
            <view class="code-decoration"></view>
          </view>
        </view>

        <view class="action-buttons">
          <t-button
            theme="primary"
            variant="outline"
            size="medium"
            icon="file-copy"
            bindtap="copyActivationCode"
            class="copy-btn"
          >
            复制激活码
          </t-button>
        </view>
      </view>

      <view class="result-actions">
        <t-button
          theme="primary"
          size="large"
          block
          bindtap="generateAgain"
          icon="refresh"
          class="generate-again-btn"
        >
          再次生成
        </t-button>
      </view>
    </view>

    <!-- 使用说明卡片 -->
    <view class="info-card mb-4">
      <view class="card-header mb-4">
        <t-icon name="help-circle" size="48rpx" color="var(--td-brand-color)" class="mr-3" />
        <text class="card-title">使用说明</text>
      </view>
      <t-cell-group>
        <t-cell title="1. 点击获取激活码按钮" left-icon="play-circle" />
        <t-cell title="2. 完整观看激励视频广告" left-icon="video" />
        <t-cell title="3. 获取唯一激活码并复制" left-icon="file-copy" />
        <t-cell title="4. 在历史记录中查看所有激活码" left-icon="time" />
      </t-cell-group>
    </view>

    <!-- 统计信息卡片 -->
    <view class="stats-card mb-4">
      <view class="card-header mb-4">
        <t-icon name="chart-bar" size="48rpx" color="var(--td-brand-color)" class="mr-3" />
        <text class="card-title">统计信息</text>
      </view>
      <t-grid column="2" class="stats-grid">
        <t-grid-item>
          <view class="stats-item">
            <text class="stats-number">{{stats.totalGenerated}}</text>
            <text class="stats-label">总生成数</text>
          </view>
        </t-grid-item>
        <t-grid-item>
          <view class="stats-item">
            <text class="stats-number">{{stats.totalAdsWatched}}</text>
            <text class="stats-label">观看广告数</text>
          </view>
        </t-grid-item>
      </t-grid>
    </view>


  </view>

  <!-- 使用指南弹窗 -->
  <guide-modal show="{{showGuideModal}}" bind:close="hideGuide"></guide-modal>

  <!-- 登录弹窗 -->
  <login-modal
    show="{{showLoginModal}}"
    allow-skip="{{true}}"
    bind:loginSuccess="onLoginSuccess"
    bind:skip="onLoginSkip"
    bind:close="onLoginClose"
  ></login-modal>

  <!-- 固定图片广告弹窗 -->
  <static-ad-modal />
</view>
