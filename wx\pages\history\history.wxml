<!--pages/history/history.wxml-->
<view class="container">
  <!-- 导航栏 -->
  <t-navbar
    title="激活码历史"
    left-arrow
    bind:go-back="goBack"
  />

  <view class="page-content">
    <!-- 统计信息卡片 -->
    <view class="stats-card card-gradient mb-4">
      <view class="stats-bg"></view>
      <view class="card-header mb-4">
        <view class="header-icon">
          <t-icon name="chart-bar" size="48rpx" color="var(--td-brand-color)" />
          <view class="icon-pulse"></view>
        </view>
        <text class="card-title">数据概览</text>
      </view>
      <view class="stats-container">
        <view class="stat-card">
          <view class="stat-icon">
            <t-icon name="file-copy" size="32rpx" color="var(--td-brand-color)" />
          </view>
          <view class="stat-content">
            <text class="stat-number">{{historyList.length}}</text>
            <text class="stat-label">总激活码</text>
          </view>
          <view class="stat-trend up">
            <t-icon name="arrow-up" size="20rpx" color="var(--td-success-color)" />
          </view>
        </view>
        <view class="stat-divider"></view>
        <view class="stat-card">
          <view class="stat-icon">
            <t-icon name="calendar" size="32rpx" color="var(--td-success-color)" />
          </view>
          <view class="stat-content">
            <text class="stat-number">{{todayCount}}</text>
            <text class="stat-label">今日生成</text>
          </view>
          <view class="stat-trend {{todayCount > 0 ? 'up' : 'neutral'}}">
            <t-icon name="{{todayCount > 0 ? 'arrow-up' : 'minus'}}" size="20rpx" color="{{todayCount > 0 ? 'var(--td-success-color)' : 'var(--td-text-color-placeholder)'}}" />
          </view>
        </view>
      </view>
    </view>

    <!-- 操作栏 -->
    <view class="action-bar mb-4" wx:if="{{historyList.length > 0}}">
      <view class="action-group">
        <t-button
          theme="light"
          variant="outline"
          size="medium"
          icon="download"
          bindtap="exportHistory"
          class="action-btn export-btn"
        >
          导出数据
        </t-button>
        <t-button
          theme="primary"
          variant="outline"
          size="medium"
          icon="chart-bar"
          bindtap="goToStats"
          class="action-btn stats-btn"
        >
          查看统计
        </t-button>
      </view>
      <t-button
        theme="danger"
        variant="outline"
        size="medium"
        icon="delete"
        bindtap="clearHistory"
        class="action-btn clear-btn"
      >
        清空记录
      </t-button>
    </view>

    <!-- 历史记录列表 -->
    <view class="history-list" wx:if="{{historyList.length > 0}}">
      <view class="list-header">
        <text class="list-title">激活码记录</text>
        <text class="list-count">共 {{historyList.length}} 条</text>
      </view>
      <view class="history-items">
        <view
          wx:for="{{historyList}}"
          wx:key="timestamp"
          class="history-item"
          bindtap="viewDetail"
          data-code="{{item.code}}"
        >
          <view class="item-left">
            <view class="item-icon">
              <t-icon name="file-copy" size="32rpx" color="var(--td-brand-color)" />
            </view>
            <view class="item-content">
              <text class="item-code">{{item.code}}</text>
              <view class="item-meta">
                <text class="item-date">{{item.date}}</text>
                <view class="item-badge">
                  <text class="badge-text">专属</text>
                </view>
              </view>
            </view>
          </view>
          <view class="item-actions">
            <t-button
              theme="primary"
              variant="text"
              size="small"
              icon="file-copy"
              bindtap="copyCode"
              data-code="{{item.code}}"
              catchtap="true"
              class="copy-action"
            >
              复制
            </t-button>
            <view class="action-indicator">
              <t-icon name="chevron-right" size="24rpx" color="var(--td-text-color-placeholder)" />
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view wx:else class="empty-state">
      <view class="empty-illustration">
        <view class="empty-icon">
          <t-icon name="file-copy" size="120rpx" color="var(--td-text-color-placeholder)" />
        </view>
        <view class="empty-decoration">
          <view class="decoration-dot"></view>
          <view class="decoration-dot"></view>
          <view class="decoration-dot"></view>
        </view>
      </view>
      <view class="empty-content">
        <text class="empty-title">暂无激活码记录</text>
        <text class="empty-desc">您还没有生成过任何激活码\n快去生成您的第一个激活码吧！</text>
      </view>
      <view class="empty-action">
        <t-button
          theme="primary"
          size="large"
          bindtap="goToIndex"
          icon="gift"
          class="generate-btn"
        >
          立即生成激活码
        </t-button>
      </view>
    </view>
  </view>
</view>
