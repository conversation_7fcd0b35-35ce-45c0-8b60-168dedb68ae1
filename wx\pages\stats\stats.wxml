<!--pages/stats/stats.wxml-->
<view class="container">
  <!-- 导航栏 -->
  <t-navbar
    title="数据统计"
    left-arrow
    bind:go-back="goBack"
  />

  <view class="page-content">
    <!-- 总体统计卡片 -->
    <view class="stats-card card-elevated mb-4">
      <view class="stats-bg"></view>
      <view class="card-header mb-4">
        <view class="header-icon">
          <t-icon name="chart-bar" size="48rpx" color="var(--td-brand-color)" />
          <view class="icon-glow"></view>
        </view>
        <text class="card-title">数据总览</text>
      </view>
      <view class="stats-grid">
        <view class="stat-card primary">
          <view class="stat-icon">
            <t-icon name="gift" size="32rpx" color="var(--td-brand-color)" />
          </view>
          <view class="stat-content">
            <text class="stat-number">{{stats.total.generated}}</text>
            <text class="stat-label">生成激活码</text>
          </view>
          <view class="stat-decoration"></view>
        </view>
        <view class="stat-card success">
          <view class="stat-icon">
            <t-icon name="play-circle" size="32rpx" color="var(--td-success-color)" />
          </view>
          <view class="stat-content">
            <text class="stat-number">{{stats.total.adsWatched}}</text>
            <text class="stat-label">观看广告</text>
          </view>
          <view class="stat-decoration"></view>
        </view>
        <view class="stat-card warning">
          <view class="stat-icon">
            <t-icon name="file-copy" size="32rpx" color="var(--td-warning-color)" />
          </view>
          <view class="stat-content">
            <text class="stat-number">{{stats.total.copied}}</text>
            <text class="stat-label">复制次数</text>
          </view>
          <view class="stat-decoration"></view>
        </view>
        <view class="stat-card info">
          <view class="stat-icon">
            <t-icon name="calendar" size="32rpx" color="var(--td-error-color)" />
          </view>
          <view class="stat-content">
            <text class="stat-number">{{stats.usage.totalDays}}</text>
            <text class="stat-label">使用天数</text>
          </view>
          <view class="stat-decoration"></view>
        </view>
      </view>
    </view>

    <!-- 今日统计卡片 -->
    <view class="today-card card-gradient mb-4">
      <view class="today-bg"></view>
      <view class="card-header mb-4">
        <view class="header-icon">
          <t-icon name="calendar" size="48rpx" color="var(--td-success-color)" />
          <view class="icon-pulse"></view>
        </view>
        <text class="card-title">今日数据</text>
        <view class="today-badge">
          <text class="badge-text">实时</text>
        </view>
      </view>
      <view class="today-stats">
        <view class="today-item">
          <view class="today-icon">
            <t-icon name="gift" size="28rpx" color="var(--td-brand-color)" />
          </view>
          <view class="today-content">
            <text class="today-number">{{stats.today.generated}}</text>
            <text class="today-label">今日生成</text>
          </view>
          <view class="today-trend {{stats.today.generated > 0 ? 'active' : ''}}">
            <t-icon name="arrow-up" size="16rpx" color="var(--td-success-color)" />
          </view>
        </view>
        <view class="today-item">
          <view class="today-icon">
            <t-icon name="play-circle" size="28rpx" color="var(--td-success-color)" />
          </view>
          <view class="today-content">
            <text class="today-number">{{stats.today.adsWatched}}</text>
            <text class="today-label">今日观看</text>
          </view>
          <view class="today-trend {{stats.today.adsWatched > 0 ? 'active' : ''}}">
            <t-icon name="arrow-up" size="16rpx" color="var(--td-success-color)" />
          </view>
        </view>
        <view class="today-item">
          <view class="today-icon">
            <t-icon name="file-copy" size="28rpx" color="var(--td-warning-color)" />
          </view>
          <view class="today-content">
            <text class="today-number">{{stats.today.copied}}</text>
            <text class="today-label">今日复制</text>
          </view>
          <view class="today-trend {{stats.today.copied > 0 ? 'active' : ''}}">
            <t-icon name="arrow-up" size="16rpx" color="var(--td-success-color)" />
          </view>
        </view>
      </view>
    </view>

    <!-- 使用情况卡片 -->
    <view class="usage-card mb-24">
      <view class="card-header mb-20">
        <t-icon name="time" size="48rpx" color="var(--td-warning-color)" class="mr-12" />
        <text class="card-title">使用情况</text>
      </view>
      <t-cell-group>
        <t-cell
          title="首次使用"
          description="{{firstUseDate}}"
          left-icon="calendar"
        />
        <t-cell
          title="最近使用"
          description="{{lastUseDate}}"
          left-icon="time"
        />
        <t-cell
          title="平均效率"
          description="{{averagePerDay}} 码/天"
          left-icon="chart-line"
        />
      </t-cell-group>
    </view>

    <!-- 成就系统卡片 -->
    <view class="achievement-card card-elevated mb-4">
      <view class="achievement-bg"></view>
      <view class="card-header mb-4">
        <view class="header-icon">
          <t-icon name="medal" size="48rpx" color="var(--td-warning-color)" />
          <view class="icon-sparkle"></view>
        </view>
        <text class="card-title">成就系统</text>
        <view class="achievement-count">
          <text class="count-text">{{achievements.filter(item => item.unlocked).length}}/{{achievements.length}}</text>
        </view>
      </view>
      <view class="achievements">
        <view class="achievement-item {{item.unlocked ? 'unlocked' : 'locked'}}" wx:for="{{achievements}}" wx:key="id">
          <view class="achievement-card-inner">
            <view class="achievement-header">
              <view class="achievement-icon-wrapper">
                <text class="achievement-icon">{{item.icon}}</text>
                <view class="icon-bg {{item.unlocked ? 'unlocked' : 'locked'}}"></view>
                <view wx:if="{{item.unlocked}}" class="unlock-effect"></view>
              </view>
              <view class="achievement-info">
                <text class="achievement-title">{{item.title}}</text>
                <text class="achievement-desc">{{item.desc}}</text>
              </view>
              <view class="achievement-status">
                <view class="status-badge {{item.unlocked ? 'unlocked' : 'locked'}}">
                  <t-icon name="{{item.unlocked ? 'check' : 'lock-on'}}" size="20rpx" color="{{item.unlocked ? 'var(--td-success-color)' : 'var(--td-text-color-placeholder)'}}" />
                  <text class="status-text">{{item.unlocked ? '已解锁' : '未解锁'}}</text>
                </view>
              </view>
            </view>
            <view class="achievement-progress" wx:if="{{!item.unlocked}}">
              <view class="progress-info">
                <text class="progress-text">进度：{{item.progress}}/{{item.target}}</text>
                <text class="progress-percent">{{Math.round(item.progress / item.target * 100)}}%</text>
              </view>
              <view class="progress-bar">
                <view class="progress-fill" style="width: {{item.progress / item.target * 100}}%">
                  <view class="progress-shine"></view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="actions">
      <t-button
        theme="primary"
        variant="outline"
        size="large"
        icon="download"
        bindtap="exportData"
        class="action-btn"
      >
        导出数据
      </t-button>
      <t-button
        theme="danger"
        variant="outline"
        size="large"
        icon="delete"
        bindtap="clearData"
        class="action-btn"
      >
        清空数据
      </t-button>
    </view>
  </view>
</view>
