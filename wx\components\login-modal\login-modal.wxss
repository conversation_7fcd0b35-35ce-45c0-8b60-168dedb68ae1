/* components/login-modal/login-modal.wxss */

.login-modal-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: var(--td-spacer-4);
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
}

.login-modal-content {
  background: var(--td-bg-color-card);
  border-radius: var(--td-radius-large);
  width: 100%;
  max-width: 600rpx;
  max-height: 90vh;
  overflow: hidden;
  overflow-y: auto;
  box-shadow: var(--td-shadow-3);
}

.login-header {
  text-align: center;
  padding: var(--td-spacer-8) var(--td-spacer-6) var(--td-spacer-6);
  background: linear-gradient(135deg, var(--td-brand-color-light) 0%, rgba(255, 255, 255, 0.8) 100%);
  position: relative;
  overflow: hidden;
}

.login-header::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(0, 82, 217, 0.1) 0%, transparent 70%);
  animation: rotate 20s linear infinite;
}

.login-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 120rpx;
  height: 8rpx;
  background: linear-gradient(90deg, var(--td-brand-color) 0%, var(--td-success-color) 100%);
  border-radius: var(--td-radius-small);
  box-shadow: 0 2rpx 8rpx rgba(0, 82, 217, 0.3);
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.app-logo {
  position: relative;
  display: inline-block;
  margin-bottom: var(--td-spacer-3);
}

.logo-icon {
  position: relative;
  z-index: 2;
  filter: drop-shadow(0 4rpx 8rpx rgba(0, 82, 217, 0.2));
}

.logo-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 140rpx;
  height: 140rpx;
  background: radial-gradient(circle, rgba(0, 82, 217, 0.2) 0%, transparent 70%);
  border-radius: 50%;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 0.7; }
  50% { transform: translate(-50%, -50%) scale(1.1); opacity: 0.3; }
}

.app-name {
  display: block;
  font-size: var(--td-font-size-xl);
  font-weight: var(--td-font-weight-bold);
  color: var(--td-text-color-primary);
  margin-bottom: var(--td-spacer-1);
  position: relative;
  z-index: 2;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.welcome-text {
  display: block;
  font-size: var(--td-font-size-base);
  color: var(--td-text-color-secondary);
  position: relative;
  z-index: 2;
  margin-bottom: var(--td-spacer-2);
}

.header-decoration {
  position: absolute;
  bottom: 20rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 6rpx;
  background: linear-gradient(90deg, transparent 0%, var(--td-brand-color) 50%, transparent 100%);
  border-radius: 3rpx;
}

.login-description {
  padding: 0 var(--td-spacer-6) var(--td-spacer-5);
}

.desc-title {
  display: block;
  font-size: var(--td-font-size-m);
  font-weight: var(--td-font-weight-semibold);
  color: var(--td-text-color-primary);
  margin-bottom: var(--td-spacer-4);
  text-align: center;
  position: relative;
}

.desc-title::after {
  content: '';
  position: absolute;
  bottom: -12rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background: var(--td-brand-color);
  border-radius: 2rpx;
}

.benefits-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--td-spacer-3);
  margin-top: var(--td-spacer-4);
}

.benefit-item {
  display: flex;
  align-items: flex-start;
  padding: var(--td-spacer-3);
  background: linear-gradient(135deg, var(--td-bg-color-card) 0%, var(--td-gray-color-1) 100%);
  border-radius: var(--td-radius-medium);
  border: 1rpx solid var(--td-border-level-1-color);
  transition: all var(--td-transition-base);
  position: relative;
  overflow: hidden;
}

.benefit-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3rpx;
  background: linear-gradient(90deg, var(--td-brand-color) 0%, var(--td-success-color) 50%, var(--td-warning-color) 100%);
}

.benefit-item:hover {
  transform: translateY(-2rpx);
  box-shadow: var(--td-shadow-1);
}

.benefit-icon {
  margin-right: var(--td-spacer-2);
  padding: var(--td-spacer-1);
  background: var(--td-bg-color-card);
  border-radius: var(--td-radius-small);
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.benefit-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.benefit-title {
  font-size: var(--td-font-size-base);
  font-weight: var(--td-font-weight-semibold);
  color: var(--td-text-color-primary);
  margin-bottom: var(--td-spacer-1);
  line-height: var(--td-line-height-tight);
}

.benefit-desc {
  font-size: var(--td-font-size-s);
  color: var(--td-text-color-secondary);
  line-height: var(--td-line-height-base);
}

.login-actions {
  padding: 0 var(--td-spacer-6) var(--td-spacer-5);
}

.login-btn-wrapper {
  position: relative;
  margin-bottom: var(--td-spacer-4);
}

.login-btn {
  position: relative;
  z-index: 2;
  box-shadow: 0 8rpx 24rpx rgba(0, 82, 217, 0.3);
  border: none;
  background: linear-gradient(135deg, var(--td-brand-color) 0%, #0034b5 100%);
  transition: all var(--td-transition-base);
}

.login-btn:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 12rpx 32rpx rgba(0, 82, 217, 0.4);
}

.login-btn:active {
  transform: translateY(0);
  box-shadow: 0 4rpx 16rpx rgba(0, 82, 217, 0.3);
}

.btn-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(0, 82, 217, 0.3) 0%, rgba(0, 52, 181, 0.3) 100%);
  border-radius: var(--td-radius-default);
  filter: blur(8rpx);
  opacity: 0;
  transition: opacity var(--td-transition-base);
}

.login-btn-wrapper:hover .btn-glow {
  opacity: 1;
}

.privacy-notice {
  text-align: center;
  line-height: var(--td-line-height-relaxed);
  padding: var(--td-spacer-3);
  background: var(--td-gray-color-1);
  border-radius: var(--td-radius-medium);
  border: 1rpx solid var(--td-border-level-1-color);
}

.privacy-text {
  font-size: var(--td-font-size-s);
  color: var(--td-text-color-placeholder);
}

.privacy-link {
  font-size: var(--td-font-size-s);
  color: var(--td-brand-color);
  text-decoration: underline;
  transition: color var(--td-transition-fast);
}

.skip-section {
  padding: 16rpx 48rpx 48rpx;
  text-align: center;
  border-top: 1rpx solid var(--td-gray-color-3);
}

.privacy-link:active {
  opacity: 0.7;
}

/* 用户信息填写表单样式 */
.user-info-form {
  padding: var(--td-spacer-5);
}

.form-header {
  text-align: center;
  margin-bottom: var(--td-spacer-7);
  padding-bottom: var(--td-spacer-4);
  border-bottom: 2rpx solid var(--td-border-level-1-color);
}

.form-title {
  display: block;
  font-size: 40rpx;
  font-weight: bold;
  color: var(--td-text-color-primary);
  margin-bottom: var(--td-spacer-2);
}

.form-desc {
  display: block;
  font-size: 28rpx;
  color: var(--td-text-color-secondary);
}

/* 头像选择样式 */
.avatar-section {
  margin-bottom: var(--td-spacer-6);
  text-align: center;
}

.section-label {
  display: block;
  font-size: 32rpx;
  font-weight: 500;
  color: var(--td-text-color-primary);
  margin-bottom: var(--td-spacer-3);
  text-align: left;
}

.avatar-button {
  position: relative;
  width: 160rpx;
  height: 160rpx;
  margin: 0 auto;
  padding: 0;
  border: none;
  border-radius: var(--td-radius-round);
  overflow: hidden;
  background: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.2s ease;
}

.avatar-button::after {
  border: none;
}

.avatar-button:active {
  transform: scale(0.95);
}

.avatar-image {
  width: 100%;
  height: 100%;
  border-radius: var(--td-radius-round);
  border: 4rpx solid var(--td-border-level-1-color);
}

.avatar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.avatar-button:active .avatar-overlay {
  opacity: 1;
}

/* 昵称输入样式 */
.nickname-section {
  margin-bottom: var(--td-spacer-6);
}

.nickname-input {
  width: 100%;
  height: 88rpx;
  padding: 0 var(--td-spacer-3);
  border: 2rpx solid var(--td-border-level-1-color);
  border-radius: var(--td-radius-default);
  font-size: 32rpx;
  color: var(--td-text-color-primary);
  background-color: var(--td-bg-color-container);
  box-sizing: border-box;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.nickname-input:focus {
  border-color: var(--td-brand-color);
  box-shadow: 0 0 0 4rpx var(--td-brand-color-light);
  outline: none;
}

.nickname-input::placeholder {
  color: var(--td-text-color-placeholder);
}

/* 表单操作按钮 */
.form-actions {
  margin-top: var(--td-spacer-5);
  padding-top: var(--td-spacer-4);
  border-top: 2rpx solid var(--td-border-level-1-color);
}

/* 响应式适配 */
@media screen and (max-width: 375px) {
  .login-modal-container {
    padding: var(--td-spacer-3);
  }

  .login-modal-content {
    max-height: 95vh;
  }

  .login-header {
    padding: var(--td-spacer-6) var(--td-spacer-4) var(--td-spacer-4);
  }

  .login-description {
    padding: 0 var(--td-spacer-4) var(--td-spacer-3);
  }

  .benefits-grid {
    grid-template-columns: 1fr;
    gap: var(--td-spacer-2);
  }

  .benefit-item {
    padding: var(--td-spacer-2);
  }

  .benefit-title {
    font-size: var(--td-font-size-s);
  }

  .benefit-desc {
    font-size: var(--td-font-size-xs);
  }

  .login-actions {
    padding: 0 var(--td-spacer-4) var(--td-spacer-3);
  }

  .user-info-form {
    padding: var(--td-spacer-4);
  }

  .form-header {
    margin-bottom: var(--td-spacer-5);
  }

  .avatar-section {
    margin-bottom: var(--td-spacer-4);
  }

  .nickname-section {
    margin-bottom: var(--td-spacer-4);
  }

  .app-name {
    font-size: var(--td-font-size-l);
  }

  .welcome-text {
    font-size: var(--td-font-size-s);
  }

  .desc-title {
    font-size: var(--td-font-size-base);
  }
}

@media screen and (min-width: 768px) {
  .login-modal-content {
    max-width: 700rpx;
  }

  .login-header {
    padding: var(--td-spacer-8) var(--td-spacer-8) var(--td-spacer-6);
  }

  .login-description {
    padding: 0 var(--td-spacer-8) var(--td-spacer-5);
  }

  .login-actions {
    padding: 0 var(--td-spacer-8) var(--td-spacer-5);
  }

  .benefits-grid {
    grid-template-columns: 1fr 1fr;
    gap: var(--td-spacer-4);
  }

  .benefit-item {
    padding: var(--td-spacer-4);
  }

  .user-info-form {
    padding: var(--td-spacer-6);
  }
}

/* 横屏适配 */
@media screen and (orientation: landscape) and (max-height: 500px) {
  .login-modal-container {
    align-items: flex-start;
    padding-top: var(--td-spacer-2);
  }

  .login-modal-content {
    max-height: 95vh;
  }

  .login-header {
    padding: var(--td-spacer-4) var(--td-spacer-6) var(--td-spacer-3);
  }

  .login-description {
    padding: 0 var(--td-spacer-6) var(--td-spacer-3);
  }

  .benefits-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: var(--td-spacer-2);
  }

  .benefit-item {
    padding: var(--td-spacer-2);
  }

  .benefit-title {
    font-size: var(--td-font-size-s);
  }

  .benefit-desc {
    font-size: var(--td-font-size-xs);
  }

  .login-actions {
    padding: 0 var(--td-spacer-6) var(--td-spacer-3);
  }

  .form-header {
    margin-bottom: var(--td-spacer-4);
  }

  .avatar-section {
    margin-bottom: var(--td-spacer-3);
  }

  .nickname-section {
    margin-bottom: var(--td-spacer-3);
  }
}

/* 超小屏幕适配 */
@media screen and (max-width: 320px) {
  .login-modal-container {
    padding: var(--td-spacer-2);
  }

  .login-header {
    padding: var(--td-spacer-4) var(--td-spacer-3) var(--td-spacer-3);
  }

  .login-description {
    padding: 0 var(--td-spacer-3) var(--td-spacer-2);
  }

  .login-actions {
    padding: 0 var(--td-spacer-3) var(--td-spacer-2);
  }

  .user-info-form {
    padding: var(--td-spacer-3);
  }

  .app-name {
    font-size: var(--td-font-size-m);
  }

  .desc-title {
    font-size: var(--td-font-size-s);
  }

  .benefit-item {
    padding: var(--td-spacer-1);
  }

  .benefit-title {
    font-size: 22rpx;
  }

  .benefit-desc {
    font-size: 20rpx;
  }
}


