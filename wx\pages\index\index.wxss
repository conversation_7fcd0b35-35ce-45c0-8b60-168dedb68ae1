/* pages/index/index.wxss */
.container {
  min-height: 100vh;
  background-color: var(--td-bg-color-page);
}

.navbar-actions {
  display: flex;
  align-items: center;
}

.page-content {
  padding: var(--td-spacer-4);
  padding-top: var(--td-spacer-2);
}

/* 页面专用卡片样式 */
.main-card,
.result-card,
.info-card,
.stats-card {
  background-color: var(--td-bg-color-card);
  border-radius: var(--td-radius-medium);
  padding: var(--td-spacer-4);
  box-shadow: var(--td-shadow-1);
  margin-bottom: var(--td-spacer-3);
}

/* 用户信息卡片 */
.user-card {
  position: relative;
  overflow: hidden;
  border: 2rpx solid var(--td-brand-color-light);
  background: linear-gradient(135deg, var(--td-brand-color-light) 0%, var(--td-bg-color-card) 100%);
}

.user-card-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(0,82,217,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

.user-info-content {
  display: flex;
  align-items: center;
  position: relative;
  z-index: 2;
  padding: var(--td-spacer-4);
}

.user-avatar-wrapper {
  position: relative;
  margin-right: var(--td-spacer-3);
}

.user-avatar {
  position: relative;
  z-index: 2;
  border: 3rpx solid var(--td-bg-color-card);
  box-shadow: 0 4rpx 12rpx rgba(0, 82, 217, 0.2);
}

.avatar-ring {
  position: absolute;
  top: -6rpx;
  left: -6rpx;
  right: -6rpx;
  bottom: -6rpx;
  border: 2rpx solid var(--td-brand-color);
  border-radius: 50%;
  opacity: 0.6;
  animation: rotate 3s linear infinite;
}

.online-indicator {
  position: absolute;
  bottom: 8rpx;
  right: 8rpx;
  width: 24rpx;
  height: 24rpx;
  background: var(--td-success-color);
  border: 3rpx solid var(--td-bg-color-card);
  border-radius: 50%;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.user-details {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.user-name {
  font-size: var(--td-font-size-l);
  font-weight: var(--td-font-weight-bold);
  color: var(--td-text-color-primary);
  margin-bottom: var(--td-spacer-1);
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

.user-desc {
  font-size: var(--td-font-size-base);
  color: var(--td-text-color-secondary);
  margin-bottom: var(--td-spacer-2);
  line-height: var(--td-line-height-base);
}

.user-stats {
  display: flex;
  align-items: center;
  gap: var(--td-spacer-2);
}

.user-stats .stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.user-stats .stat-number {
  font-size: var(--td-font-size-m);
  font-weight: var(--td-font-weight-bold);
  color: var(--td-brand-color);
  line-height: 1;
}

.user-stats .stat-label {
  font-size: var(--td-font-size-xs);
  color: var(--td-text-color-placeholder);
  margin-top: 2rpx;
}

.stat-divider {
  width: 1rpx;
  height: 32rpx;
  background: var(--td-border-level-1-color);
}

.user-badge {
  align-self: flex-start;
}

/* 主功能卡片 */
.main-card {
  text-align: center;
  position: relative;
  overflow: hidden;
}

.main-card-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 20%, rgba(0, 82, 217, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 70% 80%, rgba(0, 167, 112, 0.1) 0%, transparent 50%);
}

.feature-icon {
  position: relative;
  display: inline-block;
  margin-bottom: var(--td-spacer-3);
}

.icon-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100rpx;
  height: 100rpx;
  background: radial-gradient(circle, rgba(0, 82, 217, 0.3) 0%, transparent 70%);
  border-radius: 50%;
  animation: pulse 2s ease-in-out infinite;
}

.feature-content {
  position: relative;
  z-index: 2;
  margin-bottom: var(--td-spacer-5);
}

.feature-title {
  display: block;
  font-size: var(--td-font-size-xl);
  font-weight: var(--td-font-weight-bold);
  color: var(--td-text-color-primary);
  margin-bottom: var(--td-spacer-2);
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

.feature-desc {
  display: block;
  font-size: var(--td-font-size-base);
  color: var(--td-text-color-secondary);
  line-height: var(--td-line-height-relaxed);
  max-width: 500rpx;
  margin: 0 auto;
}

.action-section {
  position: relative;
  z-index: 2;
}

.btn-wrapper {
  position: relative;
  margin-bottom: var(--td-spacer-4);
}

.main-action-btn {
  background: linear-gradient(135deg, var(--td-brand-color) 0%, #0034b5 100%);
  box-shadow: 0 8rpx 24rpx rgba(0, 82, 217, 0.3);
  border: none;
  position: relative;
  z-index: 2;
  transition: all var(--td-transition-base);
}

.main-action-btn:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 12rpx 32rpx rgba(0, 82, 217, 0.4);
}

.btn-shine {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  border-radius: var(--td-radius-default);
  animation: shine 3s ease-in-out infinite;
}

@keyframes shine {
  0% { left: -100%; }
  50% { left: 100%; }
  100% { left: 100%; }
}

.ad-status-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--td-spacer-2);
  padding: var(--td-spacer-2);
  background: var(--td-gray-color-1);
  border-radius: var(--td-radius-medium);
  border: 1rpx solid var(--td-border-level-1-color);
}

.status-indicator {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  transition: all var(--td-transition-base);
}

.status-indicator.ready {
  background: var(--td-success-color);
  box-shadow: 0 0 8rpx rgba(0, 167, 112, 0.5);
}

.status-indicator.loading {
  background: var(--td-warning-color);
  animation: blink 1.5s ease-in-out infinite;
}

@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.3; }
}

.status-text {
  font-size: var(--td-font-size-s);
  color: var(--td-text-color-secondary);
  font-weight: var(--td-font-weight-medium);
}
/* 激活码结果卡片 */
.result-card {
  text-align: center;
  position: relative;
  overflow: hidden;
}

.success-animation {
  position: relative;
  margin-bottom: var(--td-spacer-4);
}

.success-circle {
  position: relative;
  z-index: 2;
  animation: bounceIn 0.6s ease-out;
}

@keyframes bounceIn {
  0% { transform: scale(0); opacity: 0; }
  50% { transform: scale(1.1); opacity: 0.8; }
  100% { transform: scale(1); opacity: 1; }
}

.success-ripple {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 120rpx;
  height: 120rpx;
  border: 2rpx solid var(--td-success-color);
  border-radius: 50%;
  opacity: 0;
  animation: ripple 2s ease-out infinite;
}

@keyframes ripple {
  0% { transform: translate(-50%, -50%) scale(0.8); opacity: 1; }
  100% { transform: translate(-50%, -50%) scale(2); opacity: 0; }
}

.result-content {
  margin-bottom: var(--td-spacer-5);
}

.result-title {
  display: block;
  font-size: var(--td-font-size-xl);
  font-weight: var(--td-font-weight-bold);
  color: var(--td-success-color);
  margin-bottom: var(--td-spacer-2);
  text-shadow: 0 1rpx 2rpx rgba(0, 167, 112, 0.2);
}

.result-desc {
  display: block;
  font-size: var(--td-font-size-base);
  color: var(--td-text-color-secondary);
  line-height: var(--td-line-height-base);
}

.code-display-section {
  margin-bottom: var(--td-spacer-5);
}

.code-container {
  background: linear-gradient(135deg, var(--td-gray-color-1) 0%, var(--td-bg-color-card) 100%);
  border: 2rpx solid var(--td-brand-color);
  border-radius: var(--td-radius-large);
  padding: var(--td-spacer-4);
  margin-bottom: var(--td-spacer-3);
  position: relative;
  overflow: hidden;
}

.code-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, var(--td-brand-color) 0%, var(--td-success-color) 50%, var(--td-brand-color) 100%);
  animation: progress 3s ease-in-out infinite;
}

@keyframes progress {
  0%, 100% { transform: translateX(-100%); }
  50% { transform: translateX(100%); }
}

.code-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--td-spacer-2);
}

.code-label {
  font-size: var(--td-font-size-s);
  color: var(--td-text-color-placeholder);
  font-weight: var(--td-font-weight-medium);
}

.code-badge {
  background: var(--td-brand-color);
  color: var(--td-text-color-anti);
  padding: 4rpx 12rpx;
  border-radius: var(--td-radius-small);
  font-size: var(--td-font-size-xs);
  font-weight: var(--td-font-weight-semibold);
}

.code-content {
  position: relative;
  padding: var(--td-spacer-3);
  background: var(--td-bg-color-card);
  border-radius: var(--td-radius-medium);
  border: 1rpx solid var(--td-border-level-1-color);
}

.activation-code {
  display: block;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Consolas', monospace;
  font-size: var(--td-font-size-m);
  font-weight: var(--td-font-weight-bold);
  color: var(--td-brand-color);
  word-break: break-all;
  letter-spacing: 2rpx;
  line-height: var(--td-line-height-base);
  text-shadow: 0 1rpx 2rpx rgba(0, 82, 217, 0.2);
}

.code-decoration {
  position: absolute;
  bottom: 8rpx;
  right: 8rpx;
  width: 40rpx;
  height: 40rpx;
  background: radial-gradient(circle, rgba(0, 82, 217, 0.1) 0%, transparent 70%);
  border-radius: 50%;
}

.action-buttons {
  display: flex;
  justify-content: center;
}

.copy-btn {
  box-shadow: 0 4rpx 12rpx rgba(0, 82, 217, 0.2);
  transition: all var(--td-transition-base);
}

.copy-btn:hover {
  transform: translateY(-1rpx);
  box-shadow: 0 6rpx 16rpx rgba(0, 82, 217, 0.3);
}

.result-actions {
  border-top: 1rpx solid var(--td-border-level-1-color);
  padding-top: var(--td-spacer-4);
}

.generate-again-btn {
  background: linear-gradient(135deg, var(--td-success-color) 0%, #00a870 100%);
  box-shadow: 0 6rpx 20rpx rgba(0, 167, 112, 0.3);
  border: none;
  transition: all var(--td-transition-base);
}

.generate-again-btn:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 24rpx rgba(0, 167, 112, 0.4);
}

/* 信息卡片 */
.info-card .card-header {
  justify-content: flex-start;
}

/* 统计卡片 */
.stats-card .card-header {
  justify-content: flex-start;
}

.stats-grid {
  margin-top: 16rpx;
}

.stats-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 32rpx 16rpx;
}

.stats-number {
  font-size: 48rpx;
  font-weight: bold;
  color: var(--td-brand-color);
  margin-bottom: 8rpx;
}

.stats-label {
  font-size: 28rpx;
  color: var(--td-text-color-secondary);
}

/* 加载容器 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400rpx;
}

.ad-section {
  text-align: center;
}

.ad-placeholder {
  height: 200rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 15rpx;
  margin-bottom: 40rpx;
  border: 2rpx dashed #007AFF;
  position: relative;
  overflow: hidden;
}

.ad-placeholder::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

.ad-text {
  color: #666;
  font-size: 30rpx;
  font-weight: 500;
}

.watch-ad-btn {
  width: 100%;
  height: 96rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 48rpx;
  font-size: 34rpx;
  font-weight: bold;
  line-height: 96rpx;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.4);
  transition: all 0.3s ease;
}

.watch-ad-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.4);
}

.watch-ad-btn:disabled {
  background: linear-gradient(135deg, #e0e0e0 0%, #bdbdbd 100%) !important;
  color: #999 !important;
  box-shadow: none !important;
  transform: none !important;
}

.activation-section {
  text-align: center;
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.success-icon {
  font-size: 100rpx;
  margin-bottom: 30rpx;
  animation: bounce 1s ease-in-out;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-20rpx);
  }
  60% {
    transform: translateY(-10rpx);
  }
}

.success-text {
  display: block;
  font-size: 36rpx;
  color: #28a745;
  margin-bottom: 40rpx;
  font-weight: bold;
}

.activation-code-container {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 3rpx solid #007AFF;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 122, 255, 0.15);
  position: relative;
  overflow: hidden;
}

.activation-code-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, #007AFF, #667eea, #007AFF);
  animation: progress 2s ease-in-out infinite;
}

@keyframes progress {
  0%, 100% { transform: translateX(-100%); }
  50% { transform: translateX(100%); }
}

.activation-code {
  display: block;
  font-size: 40rpx;
  font-weight: bold;
  color: #007AFF;
  font-family: 'Courier New', monospace;
  margin-bottom: 30rpx;
  word-break: break-all;
  letter-spacing: 2rpx;
}

.copy-btn {
  background: linear-gradient(135deg, #007AFF 0%, #0056CC 100%);
  color: white;
  border: none;
  border-radius: 25rpx;
  padding: 15rpx 40rpx;
  font-size: 28rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 16rpx rgba(0, 122, 255, 0.3);
}

.copy-btn:active {
  transform: translateY(1rpx);
  box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.3);
}

.generate-again-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  border: none;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
  line-height: 88rpx;
  box-shadow: 0 6rpx 20rpx rgba(40, 167, 69, 0.3);
}

.generate-again-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 3rpx 10rpx rgba(40, 167, 69, 0.3);
}

.info-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  position: relative;
  padding-left: 30rpx;
}

.info-title::before {
  content: '💡';
  position: absolute;
  left: 0;
  top: 0;
  font-size: 32rpx;
}

.info-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.info-item {
  display: flex;
  align-items: center;
  font-size: 30rpx;
  color: #555;
  line-height: 1.6;
  padding: 15rpx 0;
  border-left: 4rpx solid #007AFF;
  padding-left: 20rpx;
  background: linear-gradient(90deg, rgba(0, 122, 255, 0.05) 0%, transparent 100%);
  border-radius: 0 10rpx 10rpx 0;
}

.stats-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  position: relative;
  padding-left: 30rpx;
}

.stats-title::before {
  content: '📊';
  position: absolute;
  left: 0;
  top: 0;
  font-size: 32rpx;
}

.stats-container {
  display: flex;
  justify-content: space-around;
  gap: 20rpx;
}

.stats-item {
  text-align: center;
  flex: 1;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 20rpx;
  padding: 30rpx 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  border: 2rpx solid rgba(0, 122, 255, 0.1);
}

.stats-number {
  display: block;
  font-size: 56rpx;
  font-weight: bold;
  color: #007AFF;
  margin-bottom: 15rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 122, 255, 0.2);
}

.stats-label {
  display: block;
  font-size: 26rpx;
  color: var(--td-text-color-secondary);
  font-weight: 500;
}

/* 响应式适配 */
@media screen and (max-width: 375px) {
  .page-content {
    padding: var(--td-spacer-3);
    padding-top: var(--td-spacer-2);
  }

  .main-card,
  .result-card,
  .info-card,
  .stats-card,
  .user-card {
    padding: var(--td-spacer-3);
    margin-bottom: var(--td-spacer-2);
  }

  .user-name {
    font-size: var(--td-font-size-m);
  }

  .feature-title {
    font-size: var(--td-font-size-l);
  }

  .result-title {
    font-size: var(--td-font-size-l);
  }

  .user-stats {
    flex-direction: column;
    gap: var(--td-spacer-1);
  }

  .user-stats .stat-item {
    flex-direction: row;
    gap: var(--td-spacer-1);
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: var(--td-spacer-2);
  }

  .benefits-grid {
    grid-template-columns: 1fr;
    gap: var(--td-spacer-2);
  }

  .action-section {
    padding: 0 var(--td-spacer-2);
  }
}

@media screen and (min-width: 768px) {
  .page-content {
    max-width: 750rpx;
    margin: 0 auto;
    padding: var(--td-spacer-6);
  }

  .user-card {
    padding: var(--td-spacer-6);
  }

  .main-card,
  .result-card,
  .info-card,
  .stats-card {
    padding: var(--td-spacer-6);
  }

  .stats-grid {
    grid-template-columns: repeat(4, 1fr);
  }

  .user-info-content {
    padding: var(--td-spacer-6);
  }
}

@media screen and (min-width: 1024px) {
  .page-content {
    max-width: 900rpx;
    padding: var(--td-spacer-8);
  }

  .stats-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: var(--td-spacer-4);
  }
}

/* 横屏适配 */
@media screen and (orientation: landscape) and (max-height: 500px) {
  .page-content {
    padding: var(--td-spacer-2) var(--td-spacer-4);
  }

  .main-card,
  .result-card,
  .info-card,
  .stats-card,
  .user-card {
    padding: var(--td-spacer-3);
    margin-bottom: var(--td-spacer-2);
  }

  .feature-content {
    margin-bottom: var(--td-spacer-3);
  }

  .result-content {
    margin-bottom: var(--td-spacer-3);
  }

  .user-stats {
    flex-direction: row;
  }
}

/* 超小屏幕适配 */
@media screen and (max-width: 320px) {
  .page-content {
    padding: var(--td-spacer-2);
  }

  .user-card,
  .main-card,
  .result-card {
    padding: var(--td-spacer-2);
  }

  .user-name {
    font-size: var(--td-font-size-base);
  }

  .feature-title {
    font-size: var(--td-font-size-m);
  }

  .activation-code {
    font-size: var(--td-font-size-base);
  }
}
